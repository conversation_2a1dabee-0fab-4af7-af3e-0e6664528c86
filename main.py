import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from sty import fg, bg, ef
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score

warnings.filterwarnings('ignore') 
pd.options.display.float_format = '{:,.0f}'.format

class SpotifySongs:
    def __init__(self):
        self.file_path = './Most Streamed Spotify Songs 2024.csv'
        self.df = None
        self.model = None
        self.load_data()
        self.prepare_model()
    
    def load_data(self):
        try:
            self.df = pd.read_csv(self.file_path, encoding='ISO-8859-1')

            self.df = self.df.dropna(subset=['Track'])
            self.df = self.df.drop_duplicates()

            numeric_columns = ['Spotify Streams', 'Spotify Playlist Count', 'Spotify Playlist Reach', 
                             'Spotify Popularity', 'YouTube Views', 'YouTube Likes', 'TikTok Posts',
                             'TikTok Likes', 'TikTok Views', 'Track Score']
            
            for col in numeric_columns:
                if col in self.df.columns:
                    self.df[col] = pd.to_numeric(self.df[col].astype(str).str.replace(',', ''), errors='coerce')
                    self.df[col] = self.df[col].fillna(0)
            
            print(f"{fg.green}{ef.bold}✔{ef.rs}{fg.rs} Data loaded successfully! Total {fg.blue}{ef.bold}{len(self.df)}{ef.rs}{fg.rs} songs")
            print(f"Data columns: {list(self.df.columns)}")
            
        except Exception as e:
            print(f"{bg.red}{ef.bold}Error:{ef.rs}{bg.rs} Data loading failed: {e}")
    
    def prepare_model(self):
        try:
            features = ['Spotify Playlist Count', 'Spotify Playlist Reach', 'YouTube Views', 'YouTube Likes']
            target = 'Spotify Streams'
            
            model_data = self.df[features + [target]].dropna()
            
            if len(model_data) > 0:
                X = model_data[features]
                y = model_data[target]
                
                X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
                
                self.model = LinearRegression()
                self.model.fit(X_train, y_train)
                
                y_pred = self.model.predict(X_test)
                r2 = r2_score(y_test, y_pred)
                rmse = np.sqrt(mean_squared_error(y_test, y_pred))
                
                print(f"{fg.green}{ef.bold}✔{ef.rs}{fg.rs} Machine learning model training completed!")
                print(f"R² Score: {r2:.3f}")
                print(f"RMSE: {rmse:,.0f}")
                
            else:
                print("Not enough data to train the model")
                
        except Exception as e:
            print(f"{bg.red}{ef.bold}Error:{ef.rs}{bg.rs} Model training failed: {e}")
    
    def search_songs(self):
        print("\nSearch Songs")
        print("=" * 50)
        
        search_term = input("Enter search keyword (song name, artist, or album): ").strip()
        
        if not search_term:
            print("Please enter a search keyword")
            return
        
        mask = (self.df['Track'].str.contains(search_term, case=False, na=False) |
                self.df['Artist'].str.contains(search_term, case=False, na=False) |
                self.df['Album Name'].str.contains(search_term, case=False, na=False))
        
        results = self.df[mask]
        
        if len(results) > 0:
            print(f"\nFound {fg.blue}{ef.bold}{len(results)}{ef.rs}{fg.rs} related songs:")

            if results is not None:
                print(results[['Track', 'Artist', 'Album Name', 'Spotify Streams', 'All Time Rank']].head(10).to_string(index=False))
            
            if len(results) > 10:
                print(f"... and {fg.blue}{ef.bold}{len(results) - 10}{ef.rs}{fg.rs} more songs")
        else:
            print("No related songs found")
    
    def add_new_song(self):
        print("\nAdd New Song")
        print("=" * 50)
        
        try:
            track = input("Song name: ").strip()
            artist = input("Artist: ").strip()
            album = input("Album name: ").strip()
            
            if not track or not artist:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Song name and artist are required")
                return
            
            try:
                streams = int(input("Spotify streams (number): ") or 0)
                playlist_count = int(input("Spotify playlist count (number): ") or 0)
                popularity = int(input("Spotify popularity (0-100): ") or 0)
                youtube_views = int(input("YouTube views (number): ") or 0)
            except ValueError:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Please enter valid numbers")
                return
            
            new_song = {
                'Track': track,
                'Artist': artist,
                'Album Name': album,
                'Spotify Streams': streams,
                'Spotify Playlist Count': playlist_count,
                'Spotify Popularity': popularity,
                'YouTube Views': youtube_views,
                'All Time Rank': self.df['All Time Rank'].max() + 1,
                'Track Score': (streams / 1000000 + popularity) / 2
            }
            
            self.df = pd.concat([self.df, pd.DataFrame([new_song])], ignore_index=True)
            self.df.to_csv(self.file_path, index=False)
            
            print(f"{fg.green}{ef.bold}✔{ef.rs}{fg.rs} Successfully added song: {track} - {artist}")
            print(f"Current dataset has {fg.blue}{ef.bold}{len(self.df)}{ef.rs}{fg.rs} songs")
            
        except Exception as e:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Failed to add song: {e}")
    
    def view_top_songs(self):
        """View top rankings"""
        print("\nView Top Rankings")
        print("=" * 50)
        
        print("Select ranking type:")
        print("1. By Spotify Streams")
        print("2. By YouTube Views")
        print("3. By YouTube Likes")
        print("4. By TikTok Views")
        print("5. By TikTok Likes")
        print("6. By Track Score")
        
        choice = input("Please select (1-6): ").strip()
        
        if choice == '1':
            top_songs = self.df.nlargest(10, 'Spotify Streams')
            title = "Top 10 Songs by Spotify Streams"
            y_col = 'Spotify Streams'
        elif choice == '2':
            top_songs = self.df.nlargest(10, 'YouTube Views')
            title = "Top 10 Songs by YouTube Views"
            y_col = 'YouTube Views'
        elif choice == '3':
            top_songs = self.df.nlargest(10, 'YouTube Likes')
            title = "Top 10 Songs by YouTube Likes"
            y_col = 'YouTube Likes'
        elif choice == '4':
            top_songs = self.df.nlargest(10, 'TikTok Views')
            title = "Top 10 Songs by TikTok Views"
            y_col = 'TikTok Views'
        elif choice == '5':
            top_songs = self.df.nlargest(10, 'TikTok Likes')
            title = "Top 10 Songs by TikTok Likes"
            y_col = 'TikTok Likes'
        elif choice == '6':
            top_songs = self.df.nlargest(10, 'Track Score')
            title = "Top 10 Songs by Track Score"
            y_col = 'Track Score'
        else:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Invalid selection")
            return
        
        print(f"\n{title}")
        print("-" * 80)
        if top_songs is not None:
            print(top_songs[['Track', 'Artist', y_col]].to_string(index=False))
        
        plt.figure(figsize=(12, 8))
        songs = [f"{row['Track'][:20]}..." if len(row['Track']) > 20 else row['Track'] 
                for _, row in top_songs.iterrows()]
        values = top_songs[y_col].values
        
        bars = plt.barh(range(len(songs)), values, color='skyblue')
        plt.yticks(range(len(songs)), songs)
        plt.xlabel(y_col)
        plt.title(title)
        plt.gca().invert_yaxis()
        
        for i, bar in enumerate(bars):
            width = bar.get_width()
            plt.text(width, bar.get_y() + bar.get_height()/2, 
                    f'{width:,.0f}', ha='left', va='center', fontsize=8)
        
        plt.tight_layout()
        plt.show()
    
    def predict_streams(self):
        print("\nPredict Spotify Streams")
        print("=" * 50)
        
        if self.model is None:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Model not trained, cannot make predictions")
            return
        
        try:
            print("Please enter the following information to predict Spotify streams:")
            
            playlist_count = float(input("Spotify playlist count: ") or 0)
            playlist_reach = float(input("Spotify playlist reach: ") or 0)
            youtube_views = float(input("YouTube views: ") or 0)
            youtube_likes = float(input("YouTube likes: ") or 0)
            
            features = np.array([[playlist_count, playlist_reach, youtube_views, youtube_likes]])
            predicted_streams = self.model.predict(features)[0]
            
            print(f"\nPrediction Result:")
            print(f"Predicted Spotify Streams: {predicted_streams:,.0f}")
        except ValueError:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Please enter valid numbers")
        except Exception as e:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Prediction failed: {e}")
    
    def view_charts(self):
        print(f"\nView Charts")
        print("=" * 50)
        
        print("Select chart type:")
        print("1. Streams Distribution Histogram")
        print("2. Artist Streams Comparison")
        print("3. Popularity vs Streams Scatter Plot")
        print("4. Platform Data Comparison")
        
        choice = input("Please select (1-4): ").strip()
        
        if choice == '1':
            self.plot_streams_distribution()
        elif choice == '2':
            self.plot_artist_comparison()
        elif choice == '3':
            self.plot_popularity_vs_streams()
        elif choice == '4':
            self.plot_platform_comparison()
        else:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Invalid selection")
    
    def plot_streams_distribution(self):
        plt.figure(figsize=(12, 6))
        
        valid_streams = self.df['Spotify Streams'].dropna()
        
        sns.histplot(valid_streams, bins=50, alpha=0.7, kde=True, color='skyblue', edgecolor='black')
        plt.axvline(valid_streams.mean(), color='red', linestyle='--', label=f'Mean ({valid_streams.mean():,.0f})')
        plt.xlabel('Spotify Streams')
        plt.ylabel('Number of Songs')
        plt.title('Spotify Streams Distribution')
        plt.yscale('log')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.show()
    
    def plot_artist_comparison(self):
        top_artists = self.df.groupby('Artist')['Spotify Streams'].sum().nlargest(10)
        
        plt.figure(figsize=(12, 8))
        artists = [artist[:20] + "..." if len(artist) > 20 else artist for artist in top_artists.index]
        streams = top_artists.values
        
        bars = sns.barplot(x=streams, y=artists, palette='viridis', orient='h')
        plt.xlabel('Total Streams')
        plt.title('Top 10 Artists by Total Streams')
        
        for i, bar in enumerate(bars.patches):
            width = bar.get_width()
            plt.text(width, bar.get_y() + bar.get_height()/2, f'{width:,.0f}', ha='left', va='center', fontsize=8)
        
        plt.tight_layout()
        plt.show()
    
    def plot_popularity_vs_streams(self):
        plt.figure(figsize=(10, 6))
        
        valid_data = self.df[['Spotify Popularity', 'Spotify Streams']].dropna()
        
        sns.scatterplot(data=valid_data, x='Spotify Popularity', y='Spotify Streams', alpha=0.6, s=30, color='purple')
        plt.xlabel('Spotify Popularity')
        plt.ylabel('Spotify Streams')
        plt.title('Popularity vs Streams Relationship')
        plt.grid(True, alpha=0.3)
        
        if len(valid_data) > 1:
            z = np.polyfit(valid_data['Spotify Popularity'], valid_data['Spotify Streams'], 1)
            p = np.poly1d(z)
            plt.plot(valid_data['Spotify Popularity'], p(valid_data['Spotify Popularity']), 
                    "r--", alpha=0.8)
        
        plt.show()
    
    def plot_platform_comparison(self):
        platforms = {
            'Spotify': self.df['Spotify Streams'].mean(),
            'YouTube': self.df['YouTube Views'].mean(),
            'TikTok': self.df['TikTok Views'].mean()
        }
        
        platforms = {k: v for k, v in platforms.items() if not pd.isna(v)}
        
        plt.figure(figsize=(10, 6))
        platform_names = list(platforms.keys())
        platform_values = list(platforms.values())

        plt.title('Platform Average Data Comparison')
        
        plt.bar(platform_names, platform_values, color=['#1DB954', '#FF0000', '#000000'])
        plt.ylabel('Average Streams/Views')
        plt.xlabel('Platforms')
        
        for i, value in enumerate(platform_values):
            plt.text(i, value, f'{value:,.0f}', ha='center', va='bottom')
        
        plt.show()
    
    def filter_data(self):
        print("\nFilter Data")
        print("=" * 50)
        
        print("Select filter criteria:")
        print("1. Filter by Artist")
        print("2. Filter by Streams Range")
        print("3. Filter by Popularity Range")
        print("4. Filter by Year")
        
        choice = input("Please select (1-4): ").strip()
        
        if choice == '1':
            artist = input("Enter artist name: ").strip()
            if not artist:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Artist name is required")
                return
            filtered_df = self.df[self.df['Artist'].str.contains(artist, case=False, na=False)]
            print(f"\nFound {fg.blue}{ef.bold}{len(filtered_df)}{ef.rs}{fg.rs} songs by {artist}")
            
        elif choice == '2':
            min_streams = float(input("Minimum streams: ") or 0)
            max_streams = float(input("Maximum streams: ") or float('inf'))
            if min_streams < 0 or max_streams < 0:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Streams must be non-negative")
                return
            if min_streams > max_streams:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Minimum streams must be less than maximum streams")
                return
            filtered_df = self.df[(self.df['Spotify Streams'] >= min_streams) & 
                                 (self.df['Spotify Streams'] <= max_streams)]
            print(f"\nFound {fg.blue}{ef.bold}{len(filtered_df)}{ef.rs}{fg.rs} songs with streams between {min_streams:,.0f} - {max_streams:,.0f}")
            
        elif choice == '3':
            min_popularity = int(input("Minimum popularity (0-100): ") or 0)
            max_popularity = int(input("Maximum popularity (0-100): ") or 100)
            if min_popularity < 0 or min_popularity > 100 or max_popularity < 0 or max_popularity > 100:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Popularity must be between 0 and 100")
                return
            if min_popularity > max_popularity:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Minimum popularity must be less than maximum popularity")
                return
            filtered_df = self.df[(self.df['Spotify Popularity'] >= min_popularity) & 
                                 (self.df['Spotify Popularity'] <= max_popularity)]
            print(f"\nFound {fg.blue}{ef.bold}{len(filtered_df)}{ef.rs}{fg.rs} songs with popularity between {min_popularity} - {max_popularity}")
            
        elif choice == '4':
            year = input("Enter year (e.g., 2023): ").strip()
            if not year:
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Year is required")
                return
            if not year.isdigit():
                print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Year must be a number")
                return
            filtered_df = self.df[self.df['Release Date'].str.contains(year, na=False)]
            print(f"\nFound {fg.blue}{ef.bold}{len(filtered_df)}{ef.rs}{fg.rs} songs released in {year}")
            
        else:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Invalid selection")
            return
        
        if len(filtered_df) > 0:
            print("\nFilter Results:")
            print(filtered_df[['Track', 'Artist', 'Spotify Streams', 'Spotify Popularity', 'Release Date']].head(10).to_string(index=False))
            
            if len(filtered_df) > 10:
                print(f"... and {fg.blue}{ef.bold}{len(filtered_df) - 10}{ef.rs}{fg.rs} more songs")
        else:
            print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} No songs found matching the criteria")
    
    def show_menu(self):
        print("\n" + "="*60)
        print("Spotify 2024 Top Songs Analysis System")
        print("="*60)
        print("1. Search Songs")
        print("2. Add New Song")
        print("3. View Top Rankings")
        print("4. Predict Streams")
        print("5. View Charts")
        print("6. Filter Data")
        print("7. Show Statistics")
        print("0. Exit Program")
        print("="*60)
    
    def show_statistics(self):
        print("\nData Statistics")
        print("=" * 50)
        
        print(f"Total Songs: {len(self.df):,}")
        print(f"Number of Artists: {self.df['Artist'].nunique():,}")
        print(f"Number of Albums: {self.df['Album Name'].nunique():,}")
        
        print(f"\nStreams Statistics:")
        print(f"   Highest Streams: {self.df['Spotify Streams'].max():,.0f}")
        print(f"   Average Streams: {self.df['Spotify Streams'].mean():,.0f}")
        print(f"   Median Streams: {self.df['Spotify Streams'].median():,.0f}")
        
        print(f"\nPopularity Statistics:")
        print(f"   Highest Popularity: {self.df['Spotify Popularity'].max()}")
        print(f"   Average Popularity: {self.df['Spotify Popularity'].mean():.1f}")
        print(f"   Median Popularity: {self.df['Spotify Popularity'].median():.1f}")
        
        print(f"\nTop 5 Artists (by Total Spotify Streams):")
        top_artists = self.df.groupby('Artist')['Spotify Streams'].sum().nlargest(5)
        for i, (artist, streams) in enumerate(top_artists.items(), 1):
            print(f"   {i}. {artist}: {streams:,.0f}")
    
    def main(self):
        try:
            while True:
                self.show_menu()
                choice = input("Please select function (0-7): ").strip()
                
                if choice == '1':
                    self.search_songs()
                elif choice == '2':
                    self.add_new_song()
                elif choice == '3':
                    self.view_top_songs()
                elif choice == '4':
                    self.predict_streams()
                elif choice == '5':
                    self.view_charts()
                elif choice == '6':
                    self.filter_data()
                elif choice == '7':
                    self.show_statistics()
                elif choice == '0':
                    print("\nThank you for using Spotify Songs Analysis System! Goodbye!")
                    break
                else:
                    print(f"{fg.red}{ef.bold}ERROR:{ef.rs}{fg.rs} Invalid selection, please try again")
                
                input("\nPress Enter to continue...")

                if
        except KeyboardInterrupt:
            print("\nThank you for using Spotify Songs Analysis System! Goodbye!")
        except Exception as e:
            print(f"{bg.red}{ef.bold}Error:{ef.rs}{bg.rs} {e}")

if __name__ == "__main__":
    s = SpotifySongs()
    s.main()